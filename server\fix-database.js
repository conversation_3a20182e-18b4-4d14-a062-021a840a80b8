// Fix database schema issues
const { Client } = require('pg');
require('dotenv').config({ path: '.env' });

async function fixDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected successfully');

    // Install vector extension
    console.log('Installing vector extension...');
    try {
      await client.query('CREATE EXTENSION IF NOT EXISTS vector;');
      console.log('✅ Vector extension installed');
    } catch (error) {
      console.log('⚠️  Vector extension installation failed:', error.message);
      console.log('This might be okay if the extension is already installed or not available');
    }

    // Check if users table has site_id column
    console.log('Checking users table structure...');
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position;
    `);
    
    console.log('Users table columns:');
    result.rows.forEach(row => {
      console.log(`  ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    const hasSiteId = result.rows.some(row => row.column_name === 'site_id');
    
    if (!hasSiteId) {
      console.log('Adding site_id column to users table...');
      
      // First, ensure sites table exists and has default site
      await client.query(`
        CREATE TABLE IF NOT EXISTS sites (
          id serial PRIMARY KEY,
          name varchar(255) NOT NULL,
          code varchar(50) UNIQUE NOT NULL,
          domains text[] DEFAULT '{}' NOT NULL,
          status boolean DEFAULT true NOT NULL,
          created_at timestamp DEFAULT now() NOT NULL,
          updated_at timestamp DEFAULT now() NOT NULL
        );
      `);
      
      // Insert default site if it doesn't exist
      await client.query(`
        INSERT INTO sites (id, name, code, domains, status, created_at, updated_at) 
        VALUES (1, 'Default Site', 'default', '{"localhost", "127.0.0.1"}', true, now(), now())
        ON CONFLICT (id) DO NOTHING;
      `);
      
      // Add site_id column to users table
      await client.query('ALTER TABLE users ADD COLUMN site_id integer NOT NULL DEFAULT 1;');
      
      // Add foreign key constraint
      await client.query(`
        ALTER TABLE users 
        ADD CONSTRAINT users_site_id_fkey 
        FOREIGN KEY (site_id) REFERENCES sites(id) 
        ON DELETE RESTRICT ON UPDATE CASCADE;
      `);
      
      // Remove default value
      await client.query('ALTER TABLE users ALTER COLUMN site_id DROP DEFAULT;');
      
      console.log('✅ Added site_id column to users table');
    } else {
      console.log('✅ Users table already has site_id column');
    }

    // Test the admin users query
    console.log('Testing admin users query...');
    try {
      const adminUsers = await client.query(`
        SELECT id, username, email, first_name, last_name, roles, is_active, created_at, updated_at 
        FROM users 
        WHERE 'ADMIN' = ANY(roles) OR 'EDITOR' = ANY(roles) OR 'SUPERADMIN' = ANY(roles)
        ORDER BY username ASC
      `);
      console.log(`✅ Found ${adminUsers.rows.length} admin users`);
      adminUsers.rows.forEach(user => {
        console.log(`  - ${user.username} (${user.roles.join(', ')})`);
      });
    } catch (error) {
      console.log('❌ Admin users query failed:', error.message);
    }

    console.log('Database fix completed successfully!');

  } catch (error) {
    console.error('Database fix failed:', error);
  } finally {
    await client.end();
  }
}

fixDatabase();
