// Comprehensive test to verify CORS fix
console.log('🧪 Testing CORS Fix Verification...\n');

async function testCORSFix() {
  const tests = [];
  
  // Test 1: Frontend Proxy Login (should work - no CORS issues)
  console.log('1. Testing Frontend Proxy Login...');
  try {
    const response = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    
    const data = await response.json();
    tests.push({
      test: 'Frontend Proxy Login',
      status: response.status,
      success: data.success,
      hasToken: !!data.token,
      result: data.success ? '✅ PASS' : '❌ FAIL'
    });
  } catch (error) {
    tests.push({
      test: 'Frontend Proxy Login',
      status: 'ERROR',
      success: false,
      error: error.message,
      result: '❌ FAIL'
    });
  }
  
  // Test 2: Direct Backend Login (would have CORS issues from browser)
  console.log('2. Testing Direct Backend Login (from Node.js - no CORS)...');
  try {
    const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    
    const data = await response.json();
    tests.push({
      test: 'Direct Backend Login (Node.js)',
      status: response.status,
      success: data.success,
      hasToken: !!data.token,
      result: data.success ? '✅ PASS' : '❌ FAIL'
    });
  } catch (error) {
    tests.push({
      test: 'Direct Backend Login (Node.js)',
      status: 'ERROR',
      success: false,
      error: error.message,
      result: '❌ FAIL'
    });
  }
  
  // Test 3: Frontend Proxy Token Verification
  console.log('3. Testing Frontend Proxy Token Verification...');
  try {
    // First get a token
    const loginResponse = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    const loginData = await loginResponse.json();
    
    if (loginData.success && loginData.token) {
      const verifyResponse = await fetch('http://localhost:16000/api/admin/verify', {
        headers: { 'Authorization': `Bearer ${loginData.token}` }
      });
      
      const verifyData = await verifyResponse.json();
      tests.push({
        test: 'Frontend Proxy Token Verification',
        status: verifyResponse.status,
        success: verifyData.success,
        hasUser: !!verifyData.user,
        result: verifyData.success ? '✅ PASS' : '❌ FAIL'
      });
    } else {
      tests.push({
        test: 'Frontend Proxy Token Verification',
        status: 'SKIP',
        success: false,
        error: 'Could not get token for verification',
        result: '⏭️ SKIP'
      });
    }
  } catch (error) {
    tests.push({
      test: 'Frontend Proxy Token Verification',
      status: 'ERROR',
      success: false,
      error: error.message,
      result: '❌ FAIL'
    });
  }
  
  // Test 4: Health Check
  console.log('4. Testing Health Check...');
  try {
    const response = await fetch('http://localhost:16001/health');
    const data = await response.json();
    tests.push({
      test: 'Backend Health Check',
      status: response.status,
      success: data.success,
      result: data.success ? '✅ PASS' : '❌ FAIL'
    });
  } catch (error) {
    tests.push({
      test: 'Backend Health Check',
      status: 'ERROR',
      success: false,
      error: error.message,
      result: '❌ FAIL'
    });
  }
  
  // Display Results
  console.log('\n📊 Test Results Summary:');
  console.log('=' .repeat(80));
  
  tests.forEach((test, index) => {
    console.log(`${index + 1}. ${test.test}: ${test.result}`);
    if (test.status !== 'ERROR' && test.status !== 'SKIP') {
      console.log(`   Status: ${test.status}, Success: ${test.success}`);
      if (test.hasToken) console.log('   ✓ Token received');
      if (test.hasUser) console.log('   ✓ User data received');
    }
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
    console.log('');
  });
  
  const passedTests = tests.filter(t => t.result === '✅ PASS').length;
  const totalTests = tests.length;
  
  console.log(`🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! CORS issue has been resolved.');
  } else {
    console.log('⚠️  Some tests failed. CORS issue may still exist.');
  }
}

// Run the test
testCORSFix().catch(console.error);
