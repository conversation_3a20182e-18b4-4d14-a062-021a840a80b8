// Fix admin password with proper bcrypt hash
import postgres from 'postgres'
import bcrypt from 'bcryptjs'

async function fixAdminPassword() {
  const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://halal_user:halal_password_2025@localhost:15633/halal_chat'
  
  console.log('Connecting to database...')
  const sql = postgres(DATABASE_URL)
  
  try {
    // Generate proper bcrypt hash for 'admin123'
    const password = 'admin123'
    const saltRounds = 10
    const hashedPassword = bcrypt.hashSync(password, saltRounds)
    
    console.log('Generated password hash:', hashedPassword)
    
    // Update the admin user with the correct password hash
    const result = await sql`
      UPDATE users 
      SET password_hash = ${hashedPassword}
      WHERE username = 'admin'
    `
    
    console.log('✅ Admin password updated successfully!')
    
    // Test the password verification
    const adminUser = await sql`
      SELECT id, username, password_hash, roles 
      FROM users 
      WHERE username = 'admin'
    `
    
    if (adminUser.length > 0) {
      const user = adminUser[0]
      const isValid = bcrypt.compareSync(password, user.password_hash)
      console.log(`Password verification test: ${isValid ? '✅ PASS' : '❌ FAIL'}`)
      console.log(`User: ${user.username}, Roles: ${user.roles.join(', ')}`)
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await sql.end()
  }
}

fixAdminPassword().catch(console.error)
