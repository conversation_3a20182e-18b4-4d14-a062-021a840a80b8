<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Issue Diagnosis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .log {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>CORS Issue Diagnosis Tool</h1>
    
    <div class="test-section">
        <h2>Environment Information</h2>
        <div id="envInfo"></div>
    </div>

    <div class="test-section">
        <h2>CORS Preflight Test</h2>
        <button onclick="testPreflight()">Test OPTIONS Request</button>
        <div id="preflightResult"></div>
    </div>

    <div class="test-section">
        <h2>Admin Login CORS Test</h2>
        <button onclick="testAdminLogin()">Test Admin Login</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>Direct Backend Test</h2>
        <button onclick="testDirectBackend()">Test Direct Backend</button>
        <div id="backendResult"></div>
    </div>

    <div class="test-section">
        <h2>Frontend Proxy Test</h2>
        <button onclick="testFrontendProxy()">Test Frontend Proxy</button>
        <div id="proxyResult"></div>
    </div>

    <script>
        // Display environment information
        function showEnvironmentInfo() {
            const envInfo = {
                currentOrigin: window.location.origin,
                userAgent: navigator.userAgent,
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                port: window.location.port,
                expectedBackendURL: 'http://localhost:16001',
                expectedFrontendURL: 'http://localhost:16000'
            };
            
            document.getElementById('envInfo').innerHTML = `
                <pre>${JSON.stringify(envInfo, null, 2)}</pre>
            `;
        }

        // Test CORS preflight request
        async function testPreflight() {
            const resultDiv = document.getElementById('preflightResult');
            resultDiv.innerHTML = '<p>Testing CORS preflight...</p>';
            
            try {
                const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>Preflight Response: ${response.status}</h3>
                        <h4>Response Headers:</h4>
                        <pre>${JSON.stringify(headers, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Preflight Failed</h3>
                        <pre>Error: ${error.message}</pre>
                    </div>
                `;
            }
        }

        // Test admin login with detailed error reporting
        async function testAdminLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<p>Testing admin login...</p>';
            
            try {
                console.log('Starting admin login test...');
                
                const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    }),
                    credentials: 'include'
                });
                
                console.log('Response received:', response.status);
                
                const responseHeaders = {};
                response.headers.forEach((value, key) => {
                    responseHeaders[key] = value;
                });
                
                let responseData;
                try {
                    responseData = await response.json();
                } catch (e) {
                    responseData = await response.text();
                }
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>Login Response: ${response.status}</h3>
                        <h4>Response Headers:</h4>
                        <pre>${JSON.stringify(responseHeaders, null, 2)}</pre>
                        <h4>Response Data:</h4>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('Login test error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Login Failed</h3>
                        <pre>Error: ${error.message}
Stack: ${error.stack}</pre>
                    </div>
                `;
            }
        }

        // Test direct backend access
        async function testDirectBackend() {
            const resultDiv = document.getElementById('backendResult');
            resultDiv.innerHTML = '<p>Testing direct backend access...</p>';
            
            try {
                const response = await fetch('http://localhost:16001/health');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Direct Backend Access Working</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Direct Backend Access Failed</h3>
                        <pre>Error: ${error.message}</pre>
                    </div>
                `;
            }
        }

        // Test frontend proxy
        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxyResult');
            resultDiv.innerHTML = '<p>Testing frontend proxy...</p>';
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="${response.ok ? 'success' : 'error'}">
                        <h3>Frontend Proxy Response: ${response.status}</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Frontend Proxy Failed</h3>
                        <pre>Error: ${error.message}</pre>
                    </div>
                `;
            }
        }

        // Initialize
        showEnvironmentInfo();
    </script>
</body>
</html>
