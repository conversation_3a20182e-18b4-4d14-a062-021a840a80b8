// Test script to check admin login functionality
async function testAdminLogin() {
  console.log('Testing admin login...');
  
  try {
    // Test direct backend login
    console.log('1. Testing direct backend login...');
    const backendResponse = await fetch('http://localhost:16001/api/sites/1/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });
    
    const backendData = await backendResponse.json();
    console.log('Backend login response:', backendData);
    
    if (backendData.success && backendData.token) {
      console.log('✅ Backend login successful');
      
      // Test token verification
      console.log('2. Testing token verification...');
      const verifyResponse = await fetch('http://localhost:16001/api/sites/1/admin/verify', {
        method: 'GET',
        headers: {
          'Authorization': `<PERSON><PERSON> ${backendData.token}`
        }
      });
      
      const verifyData = await verifyResponse.json();
      console.log('Token verification response:', verifyData);
      
      if (verifyData.success) {
        console.log('✅ Token verification successful');
      } else {
        console.log('❌ Token verification failed');
      }
    } else {
      console.log('❌ Backend login failed');
    }
    
    // Test frontend proxy login
    console.log('3. Testing frontend proxy login...');
    const frontendResponse = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });
    
    const frontendData = await frontendResponse.json();
    console.log('Frontend proxy login response:', frontendData);
    
    if (frontendData.success && frontendData.token) {
      console.log('✅ Frontend proxy login successful');
      
      // Test frontend proxy verification
      console.log('4. Testing frontend proxy verification...');
      const frontendVerifyResponse = await fetch('http://localhost:16000/api/admin/verify', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${frontendData.token}`
        }
      });
      
      const frontendVerifyData = await frontendVerifyResponse.json();
      console.log('Frontend proxy verification response:', frontendVerifyData);
      
      if (frontendVerifyData.success) {
        console.log('✅ Frontend proxy verification successful');
      } else {
        console.log('❌ Frontend proxy verification failed');
      }
    } else {
      console.log('❌ Frontend proxy login failed');
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testAdminLogin();
