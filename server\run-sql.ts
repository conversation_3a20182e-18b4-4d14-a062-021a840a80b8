// Run SQL script to create basic tables
import { readFileSync } from 'fs'
import postgres from 'postgres'

async function runSQL() {
  const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://halal_user:halal_password_2025@localhost:15633/halal_chat'
  
  console.log('Connecting to database...')
  const sql = postgres(DATABASE_URL)
  
  try {
    // Read and execute the SQL file
    const sqlContent = readFileSync('./create-basic-tables.sql', 'utf8')
    console.log('Executing SQL script...')
    
    await sql.unsafe(sqlContent)
    console.log('✅ SQL script executed successfully!')
    
    // Test the admin user query
    console.log('Testing admin user query...')
    const adminUsers = await sql`
      SELECT id, username, email, first_name, last_name, roles, is_active, created_at, updated_at 
      FROM users 
      WHERE 'ADMIN' = ANY(roles) OR 'EDITOR' = ANY(roles) OR 'SUPERADMIN' = ANY(roles)
      ORDER BY username ASC
    `
    
    console.log(`✅ Found ${adminUsers.length} admin users:`)
    adminUsers.forEach(user => {
      console.log(`  - ${user.username} (${user.roles.join(', ')})`)
    })
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await sql.end()
  }
}

runSQL().catch(console.error)
