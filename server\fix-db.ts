// Fix database schema issues using existing infrastructure
import DatabaseService from './src/services/database'

async function fixDatabase() {
  console.log('Starting database fix...')
  
  const env = {
    DATABASE_URL: process.env.DATABASE_URL || 'postgresql://halal_user:halal_password_2025@localhost:15633/halal_chat'
  }
  
  const dbService = new DatabaseService(env)
  
  try {
    // Test basic connection
    console.log('Testing database connection...')
    
    // Try to get admin users to see if the query works
    console.log('Testing admin users query...')
    const adminUsers = await dbService.getAllAdminUsers()
    console.log(`✅ Found ${adminUsers.length} admin users`)
    
    adminUsers.forEach(user => {
      console.log(`  - ${user.username} (${user.roles?.join(', ') || 'no roles'})`)
    })
    
    console.log('Database appears to be working correctly!')
    
  } catch (error) {
    console.error('Database error:', error)
    console.log('This suggests there might be a schema mismatch or connection issue')
  }
}

// Run the fix
fixDatabase().catch(console.error)
