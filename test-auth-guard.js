// Test authentication guard functionality
console.log('Testing authentication guard...');

async function testAuthGuard() {
  console.log('\n=== Testing Authentication Guard ===\n');
  
  try {
    // Test 1: Access dashboard without authentication
    console.log('1. Testing dashboard access without authentication...');
    const dashboardResponse = await fetch('http://localhost:16000/en/admin/dashboard', {
      redirect: 'manual' // Don't follow redirects automatically
    });
    
    console.log('Dashboard response status:', dashboardResponse.status);
    console.log('Dashboard response headers:', Object.fromEntries(dashboardResponse.headers.entries()));
    
    if (dashboardResponse.status === 302 || dashboardResponse.status === 301) {
      const location = dashboardResponse.headers.get('location');
      console.log('✅ Dashboard properly redirects to:', location);
    } else if (dashboardResponse.status === 200) {
      console.log('⚠️  Dashboard accessible without authentication (may be client-side protected)');
    } else {
      console.log('❌ Unexpected response status:', dashboardResponse.status);
    }
    
    // Test 2: Access protected API endpoints without authentication
    console.log('\n2. Testing protected API endpoints without authentication...');
    
    const protectedEndpoints = [
      '/api/admin/users',
      '/api/admin/bots',
      '/api/admin/sessions',
      '/api/admin/whatsapp/config',
      '/api/admin/facebook/config'
    ];
    
    for (const endpoint of protectedEndpoints) {
      try {
        const response = await fetch(`http://localhost:16000${endpoint}`);
        console.log(`${endpoint}: ${response.status}`);
        
        if (response.status === 401 || response.status === 403) {
          console.log(`✅ ${endpoint} properly protected`);
        } else if (response.status === 200) {
          console.log(`⚠️  ${endpoint} accessible without authentication`);
        } else {
          console.log(`❓ ${endpoint} returned ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint} error:`, error.message);
      }
    }
    
    // Test 3: Access with valid token
    console.log('\n3. Testing access with valid authentication token...');
    
    // First get a valid token
    const loginResponse = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    
    const loginData = await loginResponse.json();
    
    if (loginData.success && loginData.token) {
      console.log('✅ Got valid token for testing');
      
      // Test protected endpoints with token
      for (const endpoint of protectedEndpoints.slice(0, 2)) { // Test first 2 endpoints
        try {
          const response = await fetch(`http://localhost:16000${endpoint}`, {
            headers: { 'Authorization': `Bearer ${loginData.token}` }
          });
          
          console.log(`${endpoint} with token: ${response.status}`);
          
          if (response.status === 200) {
            console.log(`✅ ${endpoint} accessible with valid token`);
          } else {
            console.log(`❌ ${endpoint} not accessible with valid token: ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ ${endpoint} with token error:`, error.message);
        }
      }
    } else {
      console.log('❌ Could not get valid token for testing');
    }
    
    // Test 4: Access with invalid token
    console.log('\n4. Testing access with invalid token...');
    
    const invalidToken = 'invalid.token.here';
    const testEndpoint = '/api/admin/users';
    
    try {
      const response = await fetch(`http://localhost:16000${testEndpoint}`, {
        headers: { 'Authorization': `Bearer ${invalidToken}` }
      });
      
      console.log(`${testEndpoint} with invalid token: ${response.status}`);
      
      if (response.status === 401 || response.status === 403) {
        console.log(`✅ Invalid token properly rejected`);
      } else {
        console.log(`❌ Invalid token accepted: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Invalid token test error:`, error.message);
    }
    
    console.log('\n=== Authentication Guard Test Complete ===\n');
    
  } catch (error) {
    console.error('Auth guard test failed:', error);
  }
}

testAuthGuard();
