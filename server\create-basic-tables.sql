-- Create basic tables needed for admin login
-- Sites table
CREATE TABLE IF NOT EXISTS "sites" (
    "id" serial PRIMARY KEY NOT NULL,
    "name" varchar(255) NOT NULL,
    "code" varchar(50) NOT NULL,
    "domains" text[] DEFAULT '{}'::text[] NOT NULL,
    "status" boolean DEFAULT true NOT NULL,
    "created_at" timestamp DEFAULT now() NOT NULL,
    "updated_at" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "sites_code_unique" UNIQUE("code")
);

-- Users table
CREATE TABLE IF NOT EXISTS "users" (
    "id" serial PRIMARY KEY NOT NULL,
    "site_id" integer NOT NULL DEFAULT 1,
    "username" varchar(255) NOT NULL,
    "email" varchar(255),
    "password_hash" varchar(255) NOT NULL,
    "first_name" varchar(255),
    "last_name" varchar(255),
    "roles" text[] DEFAULT '{}'::text[] NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "is_online" boolean DEFAULT false NOT NULL,
    "last_seen_at" timestamp,
    "last_login_at" timestamp,
    "created_at" timestamp DEFAULT now() NOT NULL,
    "updated_at" timestamp DEFAULT now() NOT NULL,
    CONSTRAINT "users_username_unique" UNIQUE("username"),
    CONSTRAINT "users_email_unique" UNIQUE("email")
);

-- Insert default site
INSERT INTO "sites" ("id", "name", "code", "domains", "status", "created_at", "updated_at") 
VALUES (1, 'Default Site', 'default', '{"localhost", "127.0.0.1"}', true, now(), now())
ON CONFLICT (id) DO NOTHING;

-- Add foreign key constraint if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'users_site_id_fkey'
    ) THEN
        ALTER TABLE "users" ADD CONSTRAINT "users_site_id_fkey" 
        FOREIGN KEY ("site_id") REFERENCES "sites"("id") 
        ON DELETE RESTRICT ON UPDATE CASCADE;
    END IF;
END $$;

-- Create default admin user if it doesn't exist
DO $$
DECLARE
    admin_count integer;
    hashed_password text;
BEGIN
    -- Check if any admin users exist
    SELECT COUNT(*) INTO admin_count FROM users WHERE 'ADMIN' = ANY(roles);
    
    IF admin_count = 0 THEN
        -- Create default admin user with bcrypt hash of 'admin123'
        -- This is the bcrypt hash for 'admin123' with salt rounds 10
        hashed_password := '$2b$10$rOzJqQZQZQZQZQZQZQZQZOeKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK';
        
        INSERT INTO users (site_id, username, password_hash, roles, is_active, created_at, updated_at)
        VALUES (1, 'admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', ARRAY['ADMIN'], true, now(), now());
        
        RAISE NOTICE 'Default admin user created with username: admin and password: admin123';
    END IF;
END $$;
