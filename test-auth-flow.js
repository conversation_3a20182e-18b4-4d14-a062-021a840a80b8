// Test the complete authentication flow
console.log('Running comprehensive authentication test...');

async function testCompleteAuthFlow() {
  console.log('\n=== Testing Authentication Flow ===\n');

  try {
    // Test 1: Frontend API Login
    console.log('1. Testing frontend API login...');
    const loginResponse = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);

    if (loginData.success && loginData.token) {
      console.log('✅ Frontend login successful');

      // Test 2: Token verification
      console.log('\n2. Testing token verification...');
      const verifyResponse = await fetch('http://localhost:16000/api/admin/verify', {
        method: 'GET',
        headers: { 'Authorization': `Bearer ${loginData.token}` }
      });

      const verifyData = await verifyResponse.json();
      console.log('Verify response:', verifyData);

      if (verifyData.success) {
        console.log('✅ Token verification successful');
      } else {
        console.log('❌ Token verification failed');
      }

      // Test 3: Check if admin page is accessible
      console.log('\n3. Testing admin page access...');
      const adminPageResponse = await fetch('http://localhost:16000/en/admin');
      console.log('Admin page status:', adminPageResponse.status);

      if (adminPageResponse.ok) {
        console.log('✅ Admin page accessible');
      } else {
        console.log('❌ Admin page not accessible');
      }

      // Test 4: Check dashboard access (would need authentication in real scenario)
      console.log('\n4. Testing dashboard page access...');
      const dashboardResponse = await fetch('http://localhost:16000/en/admin/dashboard');
      console.log('Dashboard page status:', dashboardResponse.status);

      if (dashboardResponse.ok) {
        console.log('✅ Dashboard page accessible');
      } else {
        console.log('❌ Dashboard page not accessible');
      }

    } else {
      console.log('❌ Frontend login failed');
      return;
    }

    // Test 5: Test with wrong credentials
    console.log('\n5. Testing with wrong credentials...');
    const wrongLoginResponse = await fetch('http://localhost:16000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'wrongpassword' })
    });

    const wrongLoginData = await wrongLoginResponse.json();
    console.log('Wrong credentials response:', wrongLoginData);

    if (!wrongLoginData.success) {
      console.log('✅ Wrong credentials properly rejected');
    } else {
      console.log('❌ Wrong credentials accepted (security issue!)');
    }

    console.log('\n=== Authentication Flow Test Complete ===\n');

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testCompleteAuthFlow();
