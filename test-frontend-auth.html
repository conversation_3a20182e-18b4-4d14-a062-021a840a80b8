<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Admin Login Test</h1>
    
    <div class="test-section">
        <h2>Manual Login Test</h2>
        <div>
            <input type="text" id="username" placeholder="Username" value="admin">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h2>Environment Check</h2>
        <button onclick="checkEnvironment()">Check Environment</button>
        <div id="envResult"></div>
    </div>

    <div class="test-section">
        <h2>API Endpoints Test</h2>
        <button onclick="testEndpoints()">Test All Endpoints</button>
        <div id="endpointsResult"></div>
    </div>

    <div class="test-section">
        <h2>CORS Test</h2>
        <button onclick="testCORS()">Test CORS</button>
        <div id="corsResult"></div>
    </div>

    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '<p>Testing login...</p>';
            
            try {
                // Test frontend API
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success && data.token) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Successful</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Test token verification
                    const verifyResponse = await fetch('/api/admin/verify', {
                        headers: {
                            'Authorization': `Bearer ${data.token}`
                        }
                    });
                    
                    const verifyData = await verifyResponse.json();
                    
                    if (verifyData.success) {
                        resultDiv.innerHTML += `
                            <div class="success">
                                <h3>✅ Token Verification Successful</h3>
                                <pre>${JSON.stringify(verifyData, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <h3>❌ Token Verification Failed</h3>
                                <pre>${JSON.stringify(verifyData, null, 2)}</pre>
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Error</h3>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }

        function checkEnvironment() {
            const resultDiv = document.getElementById('envResult');
            
            const envInfo = {
                location: window.location.href,
                userAgent: navigator.userAgent,
                localStorage: typeof(Storage) !== "undefined",
                fetch: typeof(fetch) !== "undefined",
                currentOrigin: window.location.origin
            };
            
            resultDiv.innerHTML = `
                <div class="success">
                    <h3>Environment Information</h3>
                    <pre>${JSON.stringify(envInfo, null, 2)}</pre>
                </div>
            `;
        }

        async function testEndpoints() {
            const resultDiv = document.getElementById('endpointsResult');
            resultDiv.innerHTML = '<p>Testing endpoints...</p>';
            
            const endpoints = [
                { name: 'Frontend Login', url: '/api/admin/login', method: 'POST' },
                { name: 'Frontend Verify', url: '/api/admin/verify', method: 'GET' },
                { name: 'Backend Login', url: 'http://localhost:16001/api/sites/1/admin/login', method: 'POST' },
                { name: 'Backend Verify', url: 'http://localhost:16001/api/sites/1/admin/verify', method: 'GET' }
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: { 'Content-Type': 'application/json' }
                    };
                    
                    if (endpoint.method === 'POST') {
                        options.body = JSON.stringify({ username: 'admin', password: 'admin123' });
                    }
                    
                    const response = await fetch(endpoint.url, options);
                    const data = await response.json();
                    
                    results.push({
                        name: endpoint.name,
                        status: response.status,
                        success: response.ok,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        name: endpoint.name,
                        status: 'ERROR',
                        success: false,
                        error: error.message
                    });
                }
            }
            
            resultDiv.innerHTML = `
                <div>
                    <h3>Endpoint Test Results</h3>
                    <pre>${JSON.stringify(results, null, 2)}</pre>
                </div>
            `;
        }

        async function testCORS() {
            const resultDiv = document.getElementById('corsResult');
            resultDiv.innerHTML = '<p>Testing CORS...</p>';
            
            try {
                const response = await fetch('http://localhost:16001/api/sites/1/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ CORS Test Successful</h3>
                        <p>Direct backend access works from browser</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ CORS Test Failed</h3>
                        <p>Direct backend access blocked: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
